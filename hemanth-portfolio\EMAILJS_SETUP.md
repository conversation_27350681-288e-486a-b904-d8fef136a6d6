# 📧 EmailJS Setup Guide for Contact Form

## 🚀 Quick Setup Steps

### 1. Create EmailJS Account
1. Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

### 2. Create Email Service
1. Go to **Email Services** in your EmailJS dashboard
2. Click **Add New Service**
3. Choose **Gmail** (recommended) or your preferred email provider
4. Connect your email account (<EMAIL>)
5. Note down your **Service ID** (e.g., `service_hemanth`)

### 3. Create Email Template
1. Go to **Email Templates** in your dashboard
2. Click **Create New Template**
3. Use this template content:

```
Subject: New Portfolio Contact Message from {{user_name}}

From: {{user_name}}
Email: {{user_email}}

Message:
{{message}}

---
This message was sent from your portfolio contact form.
```

4. Save the template and note down your **Template ID** (e.g., `template_portfolio`)

### 4. Get Public Key
1. Go to **Account** → **General**
2. Find your **Public Key** (e.g., `your_public_key_here`)

### 5. Update Contact.jsx
Replace these values in `src/components/Contact.jsx`:

```javascript
emailjs.sendForm(
  'service_hemanth',        // Your Service ID
  'template_portfolio',     // Your Template ID
  form.current,
  'your_public_key_here'    // Your Public Key
)
```

### 6. Test the Contact Form
1. Run your portfolio: `npm start`
2. Go to Contact section
3. Fill out the form and submit
4. Check your email for the message!

## 🎯 Benefits
- ✅ Receive messages directly in your email
- ✅ Free tier allows 200 emails/month
- ✅ No backend server needed
- ✅ Professional contact system
- ✅ Auto-reply capability

## 📝 Current Configuration
- **Email**: <EMAIL>
- **Phone**: +91 **********
- **Location**: Rajahmundry, AP, INDIA
- **Form Fields**: Name, Email, Message

## 🔧 Troubleshooting
- Make sure all IDs are correct
- Check spam folder for test emails
- Verify email service connection
- Ensure template variables match form field names

---
**Note**: Update the Service ID, Template ID, and Public Key in Contact.jsx after completing the setup!
