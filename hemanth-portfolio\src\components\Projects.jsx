import React from 'react';
import { motion } from 'framer-motion';
import { FiGithub, FiExternalLink, FiCode, FiShoppingCart } from 'react-icons/fi';
import { FaLeaf, FaMapMarkedAlt } from 'react-icons/fa';

const Projects = () => {
  const projects = [
    {
      id: 1,
      title: 'Smart Farming App',
      description: 'A comprehensive IoT-based smart farming solution that helps farmers monitor soil conditions, weather patterns, and crop health in real-time. Features include automated irrigation, pest detection, and yield prediction using machine learning.',
      image: '/api/placeholder/600/400',
      technologies: ['React', 'Node.js', 'MongoDB', 'IoT', 'Machine Learning'],
      github: 'https://github.com/Hemanth-konduri/smart-farming',
      demo: 'https://smart-farming-demo.netlify.app',
      icon: FaLeaf,
      color: 'from-green-400 to-emerald-600'
    },
    {
      id: 2,
      title: 'Travel Guide App',
      description: 'An interactive travel companion app that provides personalized recommendations, local insights, and real-time navigation. Users can discover hidden gems, plan itineraries, and connect with local guides.',
      image: '/api/placeholder/600/400',
      technologies: ['React Native', 'Express.js', 'PostgreSQL', 'Maps API', 'Firebase'],
      github: 'https://github.com/Hemanth-konduri/travel-guide',
      demo: 'https://travel-guide-demo.netlify.app',
      icon: FaMapMarkedAlt,
      color: 'from-blue-400 to-cyan-600'
    },
    {
      id: 3,
      title: 'E-commerce Platform',
      description: 'A full-featured e-commerce platform with advanced product filtering, secure payment integration, inventory management, and analytics dashboard. Built with modern web technologies for optimal performance.',
      image: '/api/placeholder/600/400',
      technologies: ['Next.js', 'Stripe', 'Prisma', 'PostgreSQL', 'Tailwind CSS'],
      github: 'https://github.com/Hemanth-konduri/ecommerce-platform',
      demo: 'https://ecommerce-demo.netlify.app',
      icon: FiShoppingCart,
      color: 'from-purple-400 to-pink-600'
    }
  ];

  return (
    <section id="projects" className="py-20 bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Featured <span className="text-neon-blue">Projects</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-purple mx-auto mb-6"></div>
          <p className="text-gray-300 text-lg max-w-3xl mx-auto">
            Here are some of my recent projects that showcase my skills in full-stack development, 
            UI/UX design, and problem-solving.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
              className="glass rounded-2xl overflow-hidden group hover:neon-blue transition-all duration-300"
            >
              {/* Project Image/Icon */}
              <div className={`relative h-48 bg-gradient-to-br ${project.color} flex items-center justify-center`}>
                <project.icon className="w-16 h-16 text-white opacity-80" />
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300"></div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-white mb-3 group-hover:text-neon-blue transition-colors duration-300">
                  {project.title}
                </h3>
                
                <p className="text-gray-300 text-sm leading-relaxed mb-4">
                  {project.description}
                </p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {project.technologies.map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded-full hover:bg-neon-blue/20 hover:text-neon-blue transition-all duration-300"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4">
                  <motion.a
                    href={project.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="flex items-center space-x-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-all duration-300 flex-1 justify-center"
                  >
                    <FiGithub className="w-4 h-4" />
                    <span className="text-sm">Code</span>
                  </motion.a>
                  
                  <motion.a
                    href={project.demo}
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="flex items-center space-x-2 px-4 py-2 bg-neon-blue hover:bg-neon-blue/80 text-white rounded-lg transition-all duration-300 flex-1 justify-center"
                  >
                    <FiExternalLink className="w-4 h-4" />
                    <span className="text-sm">Demo</span>
                  </motion.a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* More Projects Button */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <motion.a
            href="https://github.com/Hemanth-konduri"
            target="_blank"
            rel="noopener noreferrer"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center space-x-2 px-8 py-4 bg-transparent border-2 border-neon-purple text-neon-purple rounded-lg font-semibold hover:bg-neon-purple hover:text-white transition-all duration-300 btn-glow"
          >
            <FiCode className="w-5 h-5" />
            <span>View More Projects</span>
          </motion.a>
        </motion.div>
      </div>
    </section>
  );
};

export default Projects;
