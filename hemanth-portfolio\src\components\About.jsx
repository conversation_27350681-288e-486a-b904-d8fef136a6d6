import React from 'react';
import { motion } from 'framer-motion';
import { FaReact, FaNodeJs, FaFigma, FaJava } from 'react-icons/fa';
import { SiMongodb, SiJavascript, SiTailwindcss, SiPython, SiGit } from 'react-icons/si';


const About = () => {
  const skills = [
    { icon: FaReact, name: 'React', color: '#61DAFB' },
    { icon: SiJavascript, name: 'JavaScript', color: '#F7DF1E' },
    { icon: SiPython, name: 'Python', color: '#3776AB' },
    { icon: FaJava, name: 'Java', color: '#ED8B00' },
    { icon: FaNodeJs, name: 'Node.js', color: '#339933' },
    { icon: SiMongodb, name: 'MongoDB', color: '#47A248' },
    { icon: SiTailwindcss, name: 'Tailwind CSS', color: '#06B6D4' },
    { icon: SiGit, name: '<PERSON><PERSON>', color: '#F05032' },
    { icon: Fa<PERSON><PERSON>, name: 'Figma', color: '#F24E1E' },
  ];

  return (
    <section id="about" className="py-20 bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            About <span className="text-neon-blue">Me</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-purple mx-auto"></div>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="glass p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-white mb-4">
                Hello! I'm Hemanth Konduri
              </h3>
              <div className="space-y-4 text-gray-300 leading-relaxed">
                <p>
                  I'm a passionate Computer Science student in my second year, with a growing
                  expertise in web development and software engineering. I love turning ideas
                  into functional applications and solving complex problems through code.
                </p>
                <p>
                  My journey began with curiosity about how technology works, and has evolved
                  into a deep passion for creating meaningful digital solutions. I'm actively
                  building projects, learning new frameworks, and preparing for my career in
                  tech through hands-on experience.
                </p>
                <p>
                  As a student, I'm eager to apply my knowledge in real-world scenarios,
                  collaborate with teams, and contribute to innovative projects. I'm always
                  excited to learn from experienced developers and grow my skills.
                </p>
              </div>

              <div className="mt-6 flex flex-wrap gap-2">
                <span className="px-3 py-1 bg-neon-blue/20 text-neon-blue rounded-full text-sm">
                  CS Student
                </span>
                <span className="px-3 py-1 bg-neon-purple/20 text-neon-purple rounded-full text-sm">
                  Quick Learner
                </span>
                <span className="px-3 py-1 bg-neon-pink/20 text-neon-pink rounded-full text-sm">
                  Future Developer
                </span>
                <span className="px-3 py-1 bg-neon-green/20 text-neon-green rounded-full text-sm">
                  Problem Solver
                </span>
              </div>
            </div>
          </motion.div>

          {/* Right Column - Skills Grid */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="glass p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-white mb-6 text-center">
                Technologies I Work With
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {skills.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.5 }}
                    whileHover={{ scale: 1.1, y: -5 }}
                    viewport={{ once: true }}
                    className="flex flex-col items-center p-4 glass rounded-xl hover:neon-blue transition-all duration-300 group"
                  >
                    <skill.icon 
                      className="w-12 h-12 mb-2 group-hover:animate-float" 
                      style={{ color: skill.color }}
                    />
                    <span className="text-sm text-gray-300 group-hover:text-white transition-colors duration-300">
                      {skill.name}
                    </span>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              viewport={{ once: true }}
              className="grid grid-cols-3 gap-4"
            >
              {[
                { number: '10+', label: 'Projects Built' },
                { number: '2nd', label: 'Year Student' },
                { number: '8+', label: 'Technologies' },
              ].map((stat, index) => (
                <div key={index} className="glass p-4 rounded-xl text-center">
                  <div className="text-2xl font-bold text-neon-blue mb-1">
                    {stat.number}
                  </div>
                  <div className="text-sm text-gray-400">
                    {stat.label}
                  </div>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <motion.a
            href="#projects"
            onClick={(e) => {
              e.preventDefault();
              document.querySelector('#projects').scrollIntoView({ behavior: 'smooth' });
            }}
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="inline-block px-8 py-4 bg-gradient-to-r from-neon-blue to-neon-purple text-white rounded-lg font-semibold hover:shadow-lg hover:shadow-neon-blue/25 transition-all duration-300"
          >
            View My Projects
          </motion.a>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
