/*
===============================================================================
🎯 HEMANTH'S RESUME EDITOR - EDIT ALL YOUR DETAILS HERE!
===============================================================================

📋 WHAT TO EDIT:
1. Work Experience (lines ~10-35) - Add your real jobs, companies, descriptions
2. Education (lines ~40-55) - Add your degrees, universities, achievements
3. Certifications (lines ~60-70) - List your actual certifications
4. Quick Stats (lines ~245-250) - Update with your real numbers
5. Resume Description (line ~100) - Write your professional summary
6. PDF File - Replace 'Hemanth_Konduri_Resume.pdf' in public folder with your actual resume

🔍 HOW TO EDIT:
- Find sections marked with 🔧 🎓 🏆 📊 📝
- Replace ALL CAPS placeholder text with your real information
- Add/remove items as needed
- Keep the same structure for animations to work properly

💡 TIPS:
- Be specific in job descriptions (mention achievements, numbers, impact)
- Use action words (developed, led, improved, created, etc.)
- List relevant technologies for each role
- Keep descriptions concise but informative
- Update the PDF download link to match your actual resume file name

===============================================================================
*/

import React from 'react';
import { motion } from 'framer-motion';
import { FiDownload, FiUser, FiBriefcase, FiAward, FiBook } from 'react-icons/fi';

const Resume = () => {
  // ========================================
  // 🚀 EDIT YOUR PROJECTS HERE (Perfect for Students!)
  // ========================================
  const projects = [
    {
      title: 'Sapra Web Application',
      type: 'Academic Project',
      period: '4 Months',
      description: 'A comprehensive modern farming platform that empowers farmers with real-time market prices for better selling decisions, AI-powered disease detection for crop health monitoring, an integrated store for purchasing farming products, and an AI chatbot to resolve farming-related queries. This full-stack application bridges the gap between traditional farming and modern technology.',
      technologies: ['React.js', 'Node.js', 'Express.js', 'MongoDB', 'Gemini API'],
      github: 'https://github.com/Hemanth-konduri/sapra-web-app',
      demo: 'YOUR_DEMO_LINK'                          // Add your demo link here if available
    },
    {
      title: 'Age Calculator',
      type: 'Personal Project',
      period: 'Short Term',
      description: 'An interactive web application that calculates precise age details when users input their date of birth. The calculator provides comprehensive time information including current age in years, months, days, hours, and minutes. Features a clean, user-friendly interface with real-time calculations and responsive design.',
      technologies: ['HTML', 'CSS', 'JavaScript'],
      github: 'https://github.com/Hemanth-konduri/age-calculator',
      demo: 'YOUR_DEMO_LINK_2'                        // Add your demo link here if available
    }
    // ADD MORE PROJECTS HERE IF NEEDED:
    // {
    //   title: 'ANOTHER PROJECT',
    //   type: 'Academic Project',
    //   period: 'ANOTHER TIMELINE',
    //   description: 'ANOTHER DESCRIPTION',
    //   technologies: ['Tech1', 'Tech2'],
    //   github: 'GITHUB_LINK',
    //   demo: 'DEMO_LINK'
    // }
  ];

  // ========================================
  // 🎓 EDIT YOUR EDUCATION HERE
  // ========================================
  const education = [
    {
      degree: 'B.Tech (Software Product Engineering)',
      institution: 'Godavari Global University, Rajahmundry',
      period: '2024 - Present',
      description: 'Currently pursuing Bachelor of Technology in Software Product Engineering. Focusing on modern software development practices, product design, and engineering principles. Gaining hands-on experience in full-stack development, software architecture, and product management.',
      gpa: 'In Progress'
    }
    // ADD MORE EDUCATION IF NEEDED (e.g., 12th grade, certifications):
    // {
    //   degree: 'Intermediate (12th Grade)',
    //   institution: 'YOUR SCHOOL NAME',
    //   period: 'YOUR PERIOD',
    //   description: 'YOUR DESCRIPTION',
    //   gpa: 'YOUR PERCENTAGE/GPA'
    // }
  ];

  // ========================================
  // 🏆 EDIT YOUR CERTIFICATIONS HERE
  // ========================================
  const certifications = [
    'Summer Course Certificate - Programming Languages (C, Python) & MS Office',
    'Environmental Awareness Certificate - 16kg Recyclable Waste Paper Contribution (8th Class)',
    'Little Champs Certificate - Quiz Talent Test Participation (8th Class)'
    // ADD MORE CERTIFICATIONS:
    // 'YOUR CERTIFICATION 4',
    // 'YOUR CERTIFICATION 5'
  ];

  const handleDownloadCV = () => {
    const link = document.createElement('a');
    link.href = '/Hemanth_Konduri_Resume.pdf';
    link.download = 'Hemanth_Konduri_Resume.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <section id="resume" className="py-20 bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            My <span className="text-neon-blue">Resume</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-purple mx-auto mb-6"></div>
          <p className="text-gray-300 text-lg max-w-3xl mx-auto mb-8">
            {/* 📝 EDIT YOUR RESUME DESCRIPTION HERE */}
            YOUR RESUME DESCRIPTION HERE - Brief overview of your professional journey and expertise.
          </p>
          
          {/* Download CV Button */}
          <motion.button
            onClick={handleDownloadCV}
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center space-x-2 px-8 py-4 bg-gradient-to-r from-neon-blue to-neon-purple text-white rounded-lg font-semibold hover:shadow-lg hover:shadow-neon-blue/25 transition-all duration-300"
          >
            <FiDownload className="w-5 h-5" />
            <span>Download CV</span>
          </motion.button>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Left Column - Experience & Education */}
          <div className="space-y-12">
            {/* Projects Section */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center space-x-3 mb-8">
                <FiBriefcase className="w-8 h-8 text-neon-blue" />
                <h3 className="text-3xl font-bold text-white">Projects</h3>
              </div>

              <div className="space-y-8">
                {projects.map((project, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.2, duration: 0.6 }}
                    viewport={{ once: true }}
                    className="glass p-6 rounded-xl relative"
                  >
                    {/* Timeline dot */}
                    <div className="absolute -left-3 top-6 w-6 h-6 bg-neon-blue rounded-full border-4 border-gray-800"></div>
                    
                    <div className="ml-4">
                      <h4 className="text-xl font-bold text-white mb-1">{project.title}</h4>
                      <p className="text-neon-blue font-semibold mb-2">{project.type}</p>
                      <p className="text-gray-400 text-sm mb-3">{project.period}</p>
                      <p className="text-gray-300 mb-4">{project.description}</p>

                      {/* Project Links */}
                      <div className="flex space-x-4 mb-4">
                        {project.github && (
                          <a
                            href={project.github}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-neon-blue hover:text-neon-purple transition-colors text-sm flex items-center space-x-1"
                          >
                            <span>GitHub</span>
                          </a>
                        )}
                        {project.demo && (
                          <a
                            href={project.demo}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-neon-green hover:text-neon-blue transition-colors text-sm flex items-center space-x-1"
                          >
                            <span>Live Demo</span>
                          </a>
                        )}
                      </div>

                      <div className="flex flex-wrap gap-2">
                        {project.technologies.map((tech, techIndex) => (
                          <span
                            key={techIndex}
                            className="px-2 py-1 bg-neon-blue/20 text-neon-blue text-xs rounded-full"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Education Section */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center space-x-3 mb-8">
                <FiBook className="w-8 h-8 text-neon-purple" />
                <h3 className="text-3xl font-bold text-white">Education</h3>
              </div>
              
              <div className="space-y-6">
                {education.map((edu, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 + index * 0.2, duration: 0.6 }}
                    viewport={{ once: true }}
                    className="glass p-6 rounded-xl"
                  >
                    <h4 className="text-xl font-bold text-white mb-1">{edu.degree}</h4>
                    <p className="text-neon-purple font-semibold mb-2">{edu.institution}</p>
                    <p className="text-gray-400 text-sm mb-3">{edu.period}</p>
                    <p className="text-gray-300 mb-2">{edu.description}</p>
                    <p className="text-neon-blue font-semibold">GPA: {edu.gpa}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Right Column - Certifications & Skills Summary */}
          <div className="space-y-12">
            {/* Certifications */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center space-x-3 mb-8">
                <FiAward className="w-8 h-8 text-neon-green" />
                <h3 className="text-3xl font-bold text-white">Certifications</h3>
              </div>
              
              <div className="glass p-6 rounded-xl">
                <div className="space-y-4">
                  {certifications.map((cert, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1, duration: 0.5 }}
                      viewport={{ once: true }}
                      className="flex items-center space-x-3 p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-all duration-300"
                    >
                      <FiAward className="w-5 h-5 text-neon-green flex-shrink-0" />
                      <span className="text-gray-300">{cert}</span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Quick Stats */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center space-x-3 mb-8">
                <FiUser className="w-8 h-8 text-neon-pink" />
                <h3 className="text-3xl font-bold text-white">Quick Stats</h3>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                {[
                  // 📊 EDIT YOUR STATS HERE - Student-focused metrics
                  { label: 'Academic Year', value: '2nd Year' },
                  { label: 'Projects Built', value: '4+' },
                  { label: 'Technologies', value: '10+' },
                  { label: 'GitHub Repos', value: '25+' }
                ].map((stat, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.6 + index * 0.1, duration: 0.5 }}
                    viewport={{ once: true }}
                    className="glass p-4 rounded-xl text-center"
                  >
                    <div className="text-2xl font-bold text-neon-blue mb-1">
                      {stat.value}
                    </div>
                    <div className="text-sm text-gray-400">
                      {stat.label}
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Resume;
