/* Tailwind directives - commented out for CDN testing */
/*
@tailwind base;
@tailwind components;
@tailwind utilities;
*/

@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'JetBrains Mono', monospace;
  background: #0a0a0a;
  color: #ffffff;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #00d4ff, #8b5cf6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #8b5cf6, #00d4ff);
}

/* Glassmorphism utility classes */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Neon glow effects */
.neon-blue {
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.neon-purple {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

/* Button hover effects */
.btn-glow:hover {
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
  transform: translateY(-2px);
}

/* Text glow animation */
.text-glow {
  animation: glow 2s ease-in-out infinite alternate;
}
