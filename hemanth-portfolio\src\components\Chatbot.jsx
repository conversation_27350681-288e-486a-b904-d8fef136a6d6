/*
===============================================================================
🤖 HEMANTH'S INTELLIGENT CHATBOT - KNOWS ALL ABOUT YOU!
===============================================================================

📋 WHAT TO EDIT:
1. Personal Information (lines ~15-25) - Your basic details
2. Skills & Technologies (lines ~30-45) - Your technical expertise  
3. Experience & Projects (lines ~50-80) - Your work history and projects
4. Education & Certifications (lines ~85-95) - Your academic background
5. Interests & Personality (lines ~100-110) - Personal touches
6. Contact Information (lines ~115-120) - How to reach you

🔍 HOW IT WORKS:
- Uses keyword matching to understand user questions
- Provides intelligent responses based on your real information
- <PERSON>les common questions about experience, skills, projects, etc.
- Maintains a conversational, professional tone

💡 CUSTOMIZE:
- Replace ALL CAPS placeholders with your real information
- Add more keywords and responses as needed
- Update the knowledge base with your actual details

===============================================================================
*/

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiMessageCircle, FiX, FiSend, FiUser, FiCpu } from 'react-icons/fi';

const Chatbot = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    {
      type: 'bot',
      text: "Hi! I'm Hemanth's AI assistant. Ask me anything about his experience, skills, projects, or background! 🚀",
      timestamp: new Date()
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  // ========================================
  // 🔧 EDIT YOUR PERSONAL INFORMATION HERE
  // ========================================
  const personalInfo = {
    name: "Hemanth Konduri",
    title: "Full Stack Developer",
    location: "Rajahmundry, Andhra Pradesh",
    experience: "2nd Year Student",
    email: "<EMAIL>",
    phone: "+91 8328534576",
    linkedin: "https://www.linkedin.com/in/hemanthkonduri0912/",
    github: "https://github.com/Hemanth-konduri",
    website: "hemanthkonduri.dev"
  };

  // ========================================
  // 💻 EDIT YOUR SKILLS & TECHNOLOGIES HERE
  // ========================================
  const skills = {
    frontend: ["YOUR FRONTEND SKILLS"],            // e.g., ["React", "Vue.js", "Angular", "TypeScript"]
    backend: ["YOUR BACKEND SKILLS"],             // e.g., ["Node.js", "Python", "Java", "Express"]
    database: ["YOUR DATABASE SKILLS"],           // e.g., ["MongoDB", "PostgreSQL", "MySQL", "Redis"]
    cloud: ["YOUR CLOUD SKILLS"],                 // e.g., ["AWS", "Google Cloud", "Azure", "Docker"]
    tools: ["YOUR TOOLS & FRAMEWORKS"]            // e.g., ["Git", "Jenkins", "Kubernetes", "Figma"]
  };

  // ========================================
  // 💼 EDIT YOUR EXPERIENCE & PROJECTS HERE
  // ========================================
  const experience = [
    {
      company: "YOUR CURRENT/RECENT COMPANY",
      role: "YOUR ROLE",
      duration: "YOUR WORK PERIOD",
      description: "YOUR KEY ACHIEVEMENTS AND RESPONSIBILITIES"
    },
    {
      company: "YOUR PREVIOUS COMPANY",
      role: "YOUR PREVIOUS ROLE", 
      duration: "PREVIOUS WORK PERIOD",
      description: "YOUR PREVIOUS ACHIEVEMENTS"
    }
  ];

  const projects = [
    {
      name: "YOUR PROJECT NAME 1",
      description: "YOUR PROJECT DESCRIPTION AND TECHNOLOGIES USED",
      impact: "YOUR PROJECT IMPACT/RESULTS"
    },
    {
      name: "YOUR PROJECT NAME 2", 
      description: "YOUR SECOND PROJECT DESCRIPTION",
      impact: "YOUR SECOND PROJECT IMPACT"
    }
  ];

  // ========================================
  // 🎓 EDIT YOUR EDUCATION & CERTIFICATIONS
  // ========================================
  const education = {
    degree: "YOUR DEGREE",                         // e.g., "Bachelor of Computer Science"
    university: "YOUR UNIVERSITY",                // e.g., "Stanford University"
    year: "YOUR GRADUATION YEAR",                 // e.g., "2020"
    gpa: "YOUR GPA (optional)"                    // e.g., "3.8/4.0"
  };

  const certifications = [
    "YOUR CERTIFICATION 1",                       // e.g., "AWS Certified Developer"
    "YOUR CERTIFICATION 2",                       // e.g., "Google Cloud Professional"
    "YOUR CERTIFICATION 3"                        // e.g., "Certified Kubernetes Admin"
  ];

  // ========================================
  // 🎯 EDIT YOUR INTERESTS & PERSONALITY
  // ========================================
  const interests = [
    "YOUR INTEREST 1",                            // e.g., "Machine Learning"
    "YOUR INTEREST 2",                            // e.g., "Open Source Contributing"
    "YOUR INTEREST 3"                             // e.g., "Tech Blogging"
  ];

  const personality = {
    workStyle: "YOUR WORK STYLE DESCRIPTION",      // e.g., "Collaborative team player who loves solving complex problems"
    motivation: "WHAT MOTIVATES YOU",              // e.g., "Building products that make a real impact on users' lives"
    strengths: ["YOUR STRENGTH 1", "YOUR STRENGTH 2", "YOUR STRENGTH 3"]
  };

  // Intelligent response system
  const getResponse = (userInput) => {
    const input = userInput.toLowerCase();
    
    // Skills questions
    if (input.includes('skill') || input.includes('technology') || input.includes('tech stack')) {
      return `I'm proficient in:\n\n🎨 Frontend: ${skills.frontend.join(', ')}\n⚙️ Backend: ${skills.backend.join(', ')}\n🗄️ Databases: ${skills.database.join(', ')}\n☁️ Cloud: ${skills.cloud.join(', ')}\n🛠️ Tools: ${skills.tools.join(', ')}`;
    }
    
    // Experience questions
    if (input.includes('experience') || input.includes('work') || input.includes('job')) {
      const expText = experience.map(exp => 
        `• ${exp.role} at ${exp.company} (${exp.duration})\n  ${exp.description}`
      ).join('\n\n');
      return `Here's my professional experience:\n\n${expText}`;
    }
    
    // Project questions
    if (input.includes('project') || input.includes('portfolio') || input.includes('built')) {
      const projText = projects.map(proj => 
        `🚀 ${proj.name}\n${proj.description}\n💡 Impact: ${proj.impact}`
      ).join('\n\n');
      return `Here are some key projects I've worked on:\n\n${projText}`;
    }
    
    // Education questions
    if (input.includes('education') || input.includes('degree') || input.includes('university') || input.includes('study')) {
      return `🎓 Education: ${education.degree} from ${education.university} (${education.year})${education.gpa ? `\n📊 GPA: ${education.gpa}` : ''}`;
    }
    
    // Certification questions
    if (input.includes('certification') || input.includes('certified')) {
      return `🏆 My certifications include:\n\n${certifications.map(cert => `• ${cert}`).join('\n')}`;
    }
    
    // Contact questions
    if (input.includes('contact') || input.includes('reach') || input.includes('email') || input.includes('phone')) {
      return `📞 You can reach me at:\n\n📧 Email: ${personalInfo.email}\n📱 Phone: ${personalInfo.phone}\n💼 LinkedIn: ${personalInfo.linkedin}\n💻 GitHub: ${personalInfo.github}`;
    }
    
    // Personal questions
    if (input.includes('about') || input.includes('who are you') || input.includes('tell me about')) {
      return `I'm ${personalInfo.name}, a ${personalInfo.title} with ${personalInfo.experience} of experience. I'm based in ${personalInfo.location} and passionate about ${personality.motivation}. My work style: ${personality.workStyle}`;
    }
    
    // Interests questions
    if (input.includes('interest') || input.includes('hobby') || input.includes('passion')) {
      return `🎯 My interests include:\n\n${interests.map(interest => `• ${interest}`).join('\n')}\n\nI'm particularly motivated by ${personality.motivation}`;
    }
    
    // Strengths questions
    if (input.includes('strength') || input.includes('good at') || input.includes('excel')) {
      return `💪 My key strengths are:\n\n${personality.strengths.map(strength => `• ${strength}`).join('\n')}`;
    }
    
    // Default responses for common greetings
    if (input.includes('hello') || input.includes('hi') || input.includes('hey')) {
      return `Hello! I'm here to tell you all about ${personalInfo.name}. What would you like to know? You can ask about skills, experience, projects, education, or anything else!`;
    }
    
    if (input.includes('help')) {
      return `I can help you learn about ${personalInfo.name}! Try asking:\n\n• "What are your skills?"\n• "Tell me about your experience"\n• "What projects have you worked on?"\n• "What's your education background?"\n• "How can I contact you?"\n• "What are your interests?"`;
    }
    
    // Default fallback
    return `That's an interesting question! I'd love to help you learn more about ${personalInfo.name}. Try asking about skills, experience, projects, education, or contact information. You can also ask for help to see what I can tell you about!`;
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage = {
      type: 'user',
      text: inputText,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    // Simulate typing delay
    setTimeout(() => {
      const botResponse = {
        type: 'bot',
        text: getResponse(inputText),
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
    }, 1000);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Floating Chat Button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        className={`fixed bottom-6 right-6 z-50 p-4 bg-gradient-to-r from-neon-blue to-neon-purple rounded-full shadow-lg hover:shadow-neon-blue/25 transition-all duration-300 ${isOpen ? 'hidden' : 'block'}`}
      >
        <FiMessageCircle className="w-6 h-6 text-white" />
      </motion.button>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className="fixed bottom-6 right-6 z-50 w-96 h-[500px] bg-gray-900 rounded-2xl shadow-2xl border border-gray-700 flex flex-col overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-neon-blue to-neon-purple p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <FiCpu className="w-6 h-6 text-white" />
                <div>
                  <h3 className="text-white font-semibold">AI Assistant</h3>
                  <p className="text-blue-100 text-sm">Ask me about Hemanth!</p>
                </div>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <FiX className="w-5 h-5" />
              </button>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex items-start space-x-2 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${message.type === 'user' ? 'bg-neon-blue' : 'bg-neon-purple'}`}>
                      {message.type === 'user' ? <FiUser className="w-4 h-4 text-white" /> : <FiCpu className="w-4 h-4 text-white" />}
                    </div>
                    <div className={`p-3 rounded-2xl ${message.type === 'user' ? 'bg-neon-blue text-white' : 'bg-gray-800 text-gray-100'}`}>
                      <p className="text-sm whitespace-pre-line">{message.text}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
              
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex justify-start"
                >
                  <div className="flex items-start space-x-2">
                    <div className="w-8 h-8 rounded-full bg-neon-purple flex items-center justify-center">
                      <FiCpu className="w-4 h-4 text-white" />
                    </div>
                    <div className="bg-gray-800 p-3 rounded-2xl">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <div className="p-4 border-t border-gray-700">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask me anything about Hemanth..."
                  className="flex-1 bg-gray-800 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-neon-blue"
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!inputText.trim()}
                  className="bg-neon-blue text-white p-2 rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FiSend className="w-4 h-4" />
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Chatbot;
