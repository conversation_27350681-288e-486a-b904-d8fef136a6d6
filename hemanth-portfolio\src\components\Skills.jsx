import React from 'react';
import { motion } from 'framer-motion';
import { FaReact, FaNodeJs, FaHtml5, FaCss3Alt, FaJs, FaGitAlt, FaFigma, FaCode } from 'react-icons/fa';
import { SiMongodb, SiExpress, SiTailwindcss } from 'react-icons/si';

const Skills = () => {
  const skillCategories = [
    {
      title: 'Frontend Development',
      skills: [
        { name: 'React', level: 70, icon: FaReact, color: '#61DAFB' },
        { name: 'JavaScript', level: 65, icon: FaJs, color: '#F7DF1E' },
        { name: 'HTML5', level: 95, icon: FaHtml5, color: '#E34F26' },
        { name: 'CSS3', level: 90, icon: FaCss3Alt, color: '#1572B6' },
        { name: 'Tailwind CSS', level: 50, icon: SiTailwindcss, color: '#06B6D4' },
      ]
    },
    {
      title: 'Backend Development',
      skills: [
        { name: 'Node.js', level: 80, icon: FaNodeJs, color: '#339933' },
        { name: 'Express.js', level: 80, icon: SiExpress, color: '#000000' },
        { name: 'MongoDB', level: 75, icon: SiMongodb, color: '#47A248' },
      ]
    },
    {
      title: 'Tools & Others',
      skills: [
        { name: 'Git', level: 88, icon: FaGitAlt, color: '#F05032' },
        { name: 'Figma', level: 85, icon: FaFigma, color: '#F24E1E' },
        { name: 'Problem Solving', level: 75, icon: FaCode, color: '#4CAF50' },
      ]
    }
  ];

  return (
    <section id="skills" className="py-20 bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            My <span className="text-neon-blue">Skills</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-purple mx-auto mb-6"></div>
          <p className="text-gray-300 text-lg max-w-3xl mx-auto">
            Here's a comprehensive overview of my technical skills and proficiency levels 
            across different technologies and tools.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {skillCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: categoryIndex * 0.2, duration: 0.8 }}
              viewport={{ once: true }}
              className="glass p-8 rounded-2xl"
            >
              <h3 className="text-2xl font-bold text-white mb-8 text-center">
                {category.title}
              </h3>
              
              <div className="space-y-6">
                {category.skills.map((skill, skillIndex) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: (categoryIndex * 0.2) + (skillIndex * 0.1), duration: 0.6 }}
                    viewport={{ once: true }}
                    className="space-y-2"
                  >
                    {/* Skill Header */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <skill.icon 
                          className="w-6 h-6" 
                          style={{ color: skill.color }}
                        />
                        <span className="text-white font-medium">{skill.name}</span>
                      </div>
                      <span className="text-neon-blue font-semibold">{skill.level}%</span>
                    </div>
                    
                    {/* Progress Bar */}
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        whileInView={{ width: `${skill.level}%` }}
                        transition={{ delay: (categoryIndex * 0.2) + (skillIndex * 0.1) + 0.3, duration: 1 }}
                        viewport={{ once: true }}
                        className="h-2 rounded-full bg-gradient-to-r from-neon-blue to-neon-purple"
                        style={{ 
                          boxShadow: '0 0 10px rgba(0, 212, 255, 0.5)' 
                        }}
                      />
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional Skills Badges */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <h3 className="text-2xl font-bold text-white text-center mb-8">
            Additional <span className="text-neon-purple">Competencies</span>
          </h3>
          
          <div className="flex flex-wrap justify-center gap-4">
            {[
              'UI/UX Design', 'Responsive Design', 'RESTful APIs'
            ].map((skill, index) => (
              <motion.span
                key={skill}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                whileHover={{ scale: 1.05, y: -2 }}
                viewport={{ once: true }}
                className="px-4 py-2 glass rounded-full text-gray-300 hover:text-neon-blue hover:border-neon-blue border border-gray-600 transition-all duration-300 cursor-default"
              >
                {skill}
              </motion.span>
            ))}
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <motion.a
            href="#contact"
            onClick={(e) => {
              e.preventDefault();
              document.querySelector('#contact').scrollIntoView({ behavior: 'smooth' });
            }}
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="inline-block px-8 py-4 bg-gradient-to-r from-neon-purple to-neon-pink text-white rounded-lg font-semibold hover:shadow-lg hover:shadow-neon-purple/25 transition-all duration-300"
          >
            Let's Work Together
          </motion.a>
        </motion.div>
      </div>
    </section>
  );
};

export default Skills;
