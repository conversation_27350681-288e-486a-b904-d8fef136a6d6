import React from 'react';
import { motion } from 'framer-motion';
import { FiGithub, FiLinkedin, FiMail, FiArrowDown } from 'react-icons/fi';
import profileImage from '../images/profile-image-nobg.png';

const Home = () => {
  const scrollToNext = () => {
    const aboutSection = document.querySelector('#about');
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Background Animation */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-neon-purple rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-neon-blue rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow"></div>
        <div className="absolute top-40 left-40 w-60 h-60 bg-neon-pink rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex flex-col lg:flex-row items-center justify-center min-h-screen gap-12 lg:gap-16">

          {/* Clean Profile Image */}
          <motion.div
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex-shrink-0"
          >
            <div className="relative">
              {/* Profile Image with Stunning Effects */}
              <motion.div
                whileHover={{
                  scale: 1.05,
                  rotate: [0, -2, 2, 0],
                  transition: { duration: 0.8 }
                }}
                animate={{
                  y: [0, -10, 0],
                  transition: { duration: 4, repeat: Infinity, ease: "easeInOut" }
                }}
                className="relative"
              >
                <img
                  src={profileImage}
                  alt="Hemanth Konduri"
                  className="h-80 md:h-96 lg:h-[28rem] w-auto object-contain filter drop-shadow-2xl"
                  style={{
                    filter: `
                      drop-shadow(0 0 20px rgba(0, 212, 255, 0.4))
                      drop-shadow(0 0 40px rgba(139, 92, 246, 0.3))
                      drop-shadow(0 0 60px rgba(244, 114, 182, 0.2))
                    `,
                    maxWidth: 'none'
                  }}
                />

                {/* Dynamic Glow Effects */}
                <motion.div
                  animate={{
                    scale: [1, 1.1, 1],
                    opacity: [0.3, 0.6, 0.3],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="absolute inset-0 bg-gradient-to-r from-neon-blue/20 via-neon-purple/20 to-neon-pink/20 rounded-3xl blur-xl"
                ></motion.div>

                {/* Orbiting Particles */}
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="absolute inset-0"
                >
                  <div className="absolute -top-8 left-1/2 w-4 h-4 bg-neon-blue rounded-full shadow-lg shadow-neon-blue/50"></div>
                  <div className="absolute top-1/2 -right-8 w-3 h-3 bg-neon-purple rounded-full shadow-lg shadow-neon-purple/50"></div>
                  <div className="absolute -bottom-8 left-1/2 w-2 h-2 bg-neon-pink rounded-full shadow-lg shadow-neon-pink/50"></div>
                  <div className="absolute top-1/2 -left-8 w-3 h-3 bg-neon-green rounded-full shadow-lg shadow-neon-green/50"></div>
                </motion.div>

                {/* Floating Sparkles */}
                <motion.div
                  animate={{
                    y: [0, -20, 0],
                    opacity: [0.4, 1, 0.4],
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="absolute -top-6 -right-6 w-2 h-2 bg-white rounded-full shadow-lg"
                ></motion.div>

                <motion.div
                  animate={{
                    y: [0, -15, 0],
                    opacity: [0.3, 0.8, 0.3],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1
                  }}
                  className="absolute -bottom-4 -left-4 w-1.5 h-1.5 bg-white rounded-full shadow-lg"
                ></motion.div>

                <motion.div
                  animate={{
                    y: [0, -25, 0],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5
                  }}
                  className="absolute top-1/4 -left-8 w-1 h-1 bg-white rounded-full shadow-lg"
                ></motion.div>
              </motion.div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex-1 text-center lg:text-left space-y-6"
          >
            {/* Greeting */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="text-neon-blue text-lg md:text-xl font-mono"
            >
              Hi, my name is
            </motion.p>

            {/* Main Title */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              className="text-3xl md:text-5xl lg:text-6xl font-bold text-white"
            >
              <span className="text-glow">Hemanth Konduri</span>
            </motion.h1>

            {/* Subtitle */}
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1, duration: 0.8 }}
              className="text-xl md:text-3xl lg:text-4xl font-bold text-gray-400"
            >
              I build useful & modern web apps
            </motion.h2>

            {/* Description */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.8 }}
              className="text-gray-300 text-base md:text-lg max-w-2xl leading-relaxed"
            >
              I'm a passionate full-stack developer specializing in creating exceptional digital experiences.
              Currently focused on building responsive web applications with modern technologies.
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.4, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start items-center mt-8"
            >
              <motion.a
                href="#projects"
                onClick={(e) => {
                  e.preventDefault();
                  document.querySelector('#projects').scrollIntoView({ behavior: 'smooth' });
                }}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-4 bg-transparent border-2 border-neon-blue text-neon-blue rounded-lg font-semibold hover:bg-neon-blue hover:text-gray-900 transition-all duration-300 btn-glow"
              >
                View My Work
              </motion.a>

              <motion.a
                href="#contact"
                onClick={(e) => {
                  e.preventDefault();
                  document.querySelector('#contact').scrollIntoView({ behavior: 'smooth' });
                }}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-4 glass border border-gray-600 text-white rounded-lg font-semibold hover:border-neon-purple hover:text-neon-purple transition-all duration-300"
              >
                Get In Touch
              </motion.a>
            </motion.div>

            {/* Social Links */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.6, duration: 0.8 }}
              className="flex justify-center lg:justify-start space-x-6 mt-8"
            >
              {[
                { icon: FiGithub, href: 'https://github.com/Hemanth-konduri', label: 'GitHub' },
                { icon: FiLinkedin, href: 'https://www.linkedin.com/in/hemanthkonduri0912/', label: 'LinkedIn' },
                { icon: FiMail, href: 'mailto:<EMAIL>', label: 'Email' },
              ].map((social, index) => (
                <motion.a
                  key={social.label}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.2, y: -3 }}
                  whileTap={{ scale: 0.9 }}
                  className="p-3 glass rounded-full text-gray-400 hover:text-neon-blue transition-all duration-300 hover:neon-blue"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.8 + index * 0.1 }}
                >
                  <social.icon className="w-6 h-6" />
                </motion.a>
              ))}
            </motion.div>
          </motion.div>
        </div>

        {/* Scroll Down Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2, duration: 0.8 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.button
            onClick={scrollToNext}
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="text-gray-400 hover:text-neon-blue transition-colors duration-300"
          >
            <FiArrowDown className="w-6 h-6" />
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default Home;
